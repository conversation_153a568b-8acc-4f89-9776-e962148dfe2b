@page "/clients"

@rendermode InteractiveAuto
@using FurryFriends.BlazorUI.Client.Models.Clients
@using FurryFriends.BlazorUI.Client.Models.Common
@using FurryFriends.BlazorUI.Client.Services.Interfaces
@using FurryFriends.BlazorUI.Client.Components.Common

<div class="client-list-container">
    <div class="client-list-header">
        <div>
            <h1>Client Directory</h1>
            <p>View and manage all your clients.</p>
        </div>
        <button class="btn btn-primary add-client-btn" @onclick="OpenCreatePopup">
            <span class="add-icon">+</span> Add New Client
        </button>
    </div>

    @if (isLoading)
    {
        <div class="loading-indicator">
            <p><em>Loading client list...</em></p>
        </div>
    }
    else if (errorMessage != null)
    {
        <div class="alert alert-danger">
            <p>@errorMessage</p>
            <button class="btn btn-primary mt-2" @onclick="LoadClients">Retry</button>
        </div>
    }
    else if (clients == null || clients.Count == 0)
    {
        <div class="alert alert-info">
            <p>No clients found.</p>
        </div>
    }
    else
    {
        <table class="table">
            <thead>
                <tr>
                    <th style="background-color:#e9ecef; text-transform:uppercase;">Name</th>
                    <th style="background-color:#e9ecef; text-transform:uppercase;">Email</th>
                    <th style="background-color:#e9ecef; text-transform:uppercase;">City</th>
                    <th style="background-color:#e9ecef; text-transform:uppercase;">Pets</th>
                    <th style="background-color:#e9ecef; text-transform:uppercase;">Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var client in clients)
                {
                    <tr style="@(clients.IndexOf(client) % 2 == 0 ? "background-color:#f8f9fa;" : "")">
                        <td>@client.Name</td>
                        <td>@client.EmailAddress</td>
                        <td>@client.City</td>
                        <td>@client.TotalPets</td>
                        <td style="display: flex; gap: 10px;">
                            <button class="btn btn-link p-0" style="color:brown;"
                                @onclick="() => OpenViewPopup(client.EmailAddress)" @onclick:preventDefault @onclick:stopPropagation>👁️</button>
                            <span style="color:steelblue; cursor:pointer">+🐾</span>
							<button class="btn btn-link p-0" style="color:lightcoral;"
								@onclick="() => OpenEditPopup(client.EmailAddress)" @onclick:preventDefault @onclick:stopPropagation>✏️</button>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    }

    @if (clients is not null)
    {
        <!-- Pagination Controls -->
        <Pagination
            CurrentPage="@currentPage"
            PageSize="@pageSize"
            TotalCount="@totalCount"
            TotalPages="@totalPages"
            HasPreviousPage="@hasPreviousPage"
            HasNextPage="@hasNextPage"
            OnPageChanged="@HandlePageChanged"
            OnPageSizeChanged="@HandlePageSizeChanged" />
    }
</div>


