using FurryFriends.UseCases.Domain.PetWalkers.Query.ListPetWalker;

namespace FurryFriends.Web.Endpoints.BookingEndpoints.GetAvailablePetWalkers;

/// <summary>
/// Endpoint for getting available PetWalkers for booking selection
/// </summary>
public class GetAvailablePetWalkers(IMediator mediator) : Endpoint<GetAvailablePetWalkersRequest, List<PetWalkerSummaryResponse>>
{
    private readonly IMediator _mediator = mediator;

    public override void Configure()
    {
        Get(GetAvailablePetWalkersRequest.Route);
        AllowAnonymous(); // Or apply appropriate authorization
        Options(x => x.WithName("GetAvailablePetWalkers_" + Guid.NewGuid().ToString()));
        Description(d => d
            .Produces<List<PetWalkerSummaryResponse>>(200)
            .Produces(400)
            .Produces(404));
    }

    public override async Task HandleAsync(GetAvailablePetWalkersRequest request, CancellationToken ct)
    {
        // For now, we'll use the existing ListPetWalkerQuery to get all active PetWalkers
        // In a real implementation, you might want to create a specific query for available PetWalkers
        var query = new ListPetWalkerQuery(1, 100, null); // Get first 100 active PetWalkers
        var result = await _mediator.Send(query, ct);

        if (!result.IsSuccess || result.Value?.RowsData == null)
        {
            await SendOkAsync(new List<PetWalkerSummaryResponse>(), ct);
            return;
        }

        var petWalkers = result.Value.RowsData
            .Where(pw => pw.IsActive && pw.IsVerified)
            .Select(pw => new PetWalkerSummaryResponse
            {
                Id = pw.Id,
                FullName = pw.FullName,
                Email = pw.Email,
                Biography = pw.Biography,
                HourlyRate = pw.HourlyRate,
                Currency = pw.Currency,
                IsActive = pw.IsActive,
                IsVerified = pw.IsVerified,
                YearsOfExperience = pw.YearsOfExperience,
                HasInsurance = pw.HasInsurance,
                HasFirstAidCertification = pw.HasFirstAidCertification,
                DailyPetWalkLimit = pw.DailyPetWalkLimit,
                BioPictureUrl = pw.BioPicture?.Uri,
                Rating = 0.0, // This would need to be calculated from reviews
                ReviewCount = 0, // This would need to be calculated from reviews
                ServiceAreas = pw.Locations ?? new List<string>()
            })
            .ToList();

        // Filter by service area if provided
        if (!string.IsNullOrEmpty(request.ServiceArea))
        {
            petWalkers = petWalkers
                .Where(pw => pw.ServiceAreas.Any(sa => 
                    sa.Contains(request.ServiceArea, StringComparison.OrdinalIgnoreCase)))
                .ToList();
        }

        await SendOkAsync(petWalkers, ct);
    }
}
