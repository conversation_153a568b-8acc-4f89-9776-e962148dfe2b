using Ardalis.GuardClauses;
using FurryFriends.UseCases.Services.BookingService;
using FurryFriends.UseCases.Domain.Bookings.Dto;

namespace FurryFriends.Web.Endpoints.BookingEndpoints.GetClientBookings;

/// <summary>
/// Endpoint for getting bookings for a specific client
/// </summary>
public class GetClientBookings(IBookingService bookingService) : Endpoint<GetClientBookingsRequest, List<BookingDto>>
{
    private readonly IBookingService _bookingService = bookingService;

    public override void Configure()
    {
        Get(GetClientBookingsRequest.Route);
        AllowAnonymous(); // Or apply appropriate authorization
        Options(x => x.WithName("GetClientBookings_" + Guid.NewGuid().ToString()));
        Description(d => d
            .Produces<List<BookingDto>>(200)
            .Produces(400)
            .Produces(404));
    }

    public override async Task HandleAsync(GetClientBookingsRequest request, CancellationToken ct)
    {
        Guard.Against.Null(request, nameof(GetClientBookingsRequest));
        Guard.Against.Default(request.ClientId, nameof(request.ClientId));
        Guard.Against.Default(request.StartDate, nameof(request.StartDate));
        Guard.Against.Default(request.EndDate, nameof(request.EndDate));

        var bookings = await _bookingService.GetClientBookingsAsync(
            request.ClientId, 
            request.StartDate, 
            request.EndDate);

        var bookingDtos = bookings.Select(b => new BookingDto(b.Id, b.StartTime, b.EndTime)).ToList();

        await SendOkAsync(bookingDtos, ct);
    }
}
